import { useCallback, useEffect, useState, useRef } from "react";
import {
  type SuggestSOFCallbackData,
  type FinSuggestSOFButtonProps,
  SuggestOfferResponse,
} from "./types";
import { postConsultPaymentOffer } from "./api";

export const useSuggestSOFButton = ({
  onPress,
  onInitial,
  onErrorCallback,
  ...props
}: FinSuggestSOFButtonProps) => {
  const [suggestOfferResponse, setSuggestOfferResponse] =
    useState<SuggestOfferResponse>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const latencyTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const isTimedOutRef = useRef<boolean>(false);

  const _initial = useCallback(async () => {
    try {
      // Reset timeout state
      isTimedOutRef.current = false;

      const payload = {
        orders: props.orders,
        specific_sof: props?.options?.specific_sof?.toUpperCase()
      }

      // Set up latency timeout if specified
      const latency = props.options?.latency;

      if (latency && latency > 0) {
        latencyTimeoutRef.current = setTimeout(() => {
          isTimedOutRef.current = true;
          setError(`Request timeout: API response time exceeded ${latency}ms`);
          setIsLoading(false);
        }, latency);
      }

      const result = await postConsultPaymentOffer(payload);

      // Clear latency timeout if request completed in time
      if (latencyTimeoutRef.current) {
        clearTimeout(latencyTimeoutRef.current);
        latencyTimeoutRef.current = undefined;
      }

      // Only process result if not timed out
      if (!isTimedOutRef.current) {
        onInitial?.(result);
        setSuggestOfferResponse(result);
      }
    } catch (err) {
      // Clear latency timeout on error
      if (latencyTimeoutRef.current) {
        clearTimeout(latencyTimeoutRef.current);
        latencyTimeoutRef.current = undefined;
      }
      if (!isTimedOutRef.current) {
        setError(err instanceof Error ? err.message : "Failed to fetch offer");
      }
    } finally {
      if (!isTimedOutRef.current) {
        setIsLoading(false);
      }
    }
  }, [props.orders, props.options?.latency, props.options?.specific_sof, onInitial]);

  const debouncedInitial = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (latencyTimeoutRef.current) {
      clearTimeout(latencyTimeoutRef.current);
    }
    setIsLoading(true);
    setError(undefined);

    timeoutRef.current = setTimeout(() => {
      _initial();
    }, 500);
  }, [_initial]);

  const _handleOnClick = useCallback(() => {
    if (isLoading) return;
    if (!suggestOfferResponse?.offers?.[0]) {
      onPress?.(undefined);
      return;
    }

    const result = {
      specified_sof: {
        pmc_id: suggestOfferResponse.offers[0].pmc_id,
        partner_code: suggestOfferResponse.offers[0].partner_code,
      },
    } as SuggestSOFCallbackData;
    onPress?.(result);
  }, [onPress, suggestOfferResponse, isLoading]);

  useEffect(() => {
    debouncedInitial();
    
    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (latencyTimeoutRef.current) {
        clearTimeout(latencyTimeoutRef.current);
      }
    };
  }, [debouncedInitial]);

  // Watch for error changes and trigger onErrorCallback
  useEffect(() => {
    if (error && onErrorCallback) {
      onErrorCallback(error);
    }
  }, [error, onErrorCallback]);

  return {
    _handleOnClick,
    suggestOfferResponse,
    isLoading,
    error,
    debouncedInitial, // Expose for testing
  };
};
