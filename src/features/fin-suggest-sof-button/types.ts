import type { ButtonHTMLAttributes, ReactNode } from "react";

type ValueOf<T> = T[keyof T];

type OrderItem = {
	app_id: number;
	amount: number;
};

type SuggestSOFCallbackData = {
	specified_sof: {
		pmc_id: number;
		partner_code: string;
	};
};

interface FinSuggestSOFButtonProps
	extends ButtonHTMLAttributes<HTMLButtonElement> {
	onPress: (result?: SuggestSOFCallbackData) => void;
	options?: {
		specific_sof?: "PAYLATER" | "INSTALLMENT" | "MINI_PAYLATER" | "SOF_UNSPECIFIED" | string;
		latency?: number; // Maximum response time in milliseconds before throwing error
	};
	orders: Array<OrderItem>;
	children?: ReactNode;
	disabled?: boolean;
	onInitial?:(rawSuggestOfferResponse: SuggestOfferResponse) => void;
	onErrorCallback?: (error: string) => void;
	fallbackComp?: ReactNode;
}

type OfferDisplayInfo = {
	icon_url: string;
	origin_amount_text: string;
	charge_amount_text: string;
	payment_term_text: string;
	error_description: string;
};

export const PromotionType = {
	PROMOTION_TYPE_DISCOUNT: "PROMOTION_TYPE_DISCOUNT",
	PROMOTION_TYPE_CASHBACK: "PROMOTION_TYPE_CASHBACK",
	PROMOTION_TYPE_LOYALTY_COIN: "PROMOTION_TYPE_LOYALTY_COIN",
};

export const OfferStatus = {
	OFFER_STATUS_AVAILABLE: "OFFER_STATUS_AVAILABLE",
	OFFER_STATUS_UNAVAILABLE: "OFFER_STATUS_UNAVAILABLE",
};

export const ProductCode = {
	PAYLATER: "PAYLATER",
	INSTALLMENT: "INSTALLMENT",
	MINI_PAYLATER: "MINI_PAYLATER",
};

type OfferPromotionDetail = {
	optimal_promo: {
		type: ValueOf<typeof PromotionType>; //"PROMOTION_TYPE_DISCOUNT" | "PROMOTION_TYPE_CASHBACK" | "PROMOTION_TYPE_VOUCHER";
		amount: string;
		id: string;
	};
};

type OfferFeeDetail = {
	total_amount: string;
	fee_items: Array<{
		type: number;
		amount: string;
	}>;
};

type Offer = {
	id: string;
	pmc_id: number;
	status: ValueOf<typeof OfferStatus>;
	product_code: ValueOf<typeof ProductCode>;
	partner_code: string;
	total_order_amount: string;
	total_charge_amount: string;
	fee_details: OfferFeeDetail;
	promo_details: OfferPromotionDetail;
	display_info: OfferDisplayInfo;
	paylater_details: Record<string, unknown>; // Adjust type if structure is known
	installment_details: Record<string, unknown>; // Adjust type if structure is known
};

type SuggestOfferResponse = {
	offers?: Array<Offer>;
	issued_at: string;
};

export type {
	FinSuggestSOFButtonProps,
	SuggestSOFCallbackData,
	OrderItem,
	Offer,
	OfferDisplayInfo,
	OfferPromotionDetail,
	OfferFeeDetail,
	SuggestOfferResponse,
	ValueOf
};
