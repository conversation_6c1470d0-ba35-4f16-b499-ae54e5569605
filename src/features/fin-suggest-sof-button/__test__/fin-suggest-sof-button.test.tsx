import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import "@testing-library/jest-dom";

import { render, screen, fireEvent } from "@testing-library/react";

import FinSuggestSOFButton from "../fin-suggest-sof-button";
import * as hooks from "../useSuggestSOFButton";
import { mockOfferResponse } from "../mock";

const mockOrders = [
  { app_id: 123, amount: 100 },
  { app_id: 456, amount: 200 },
];

const mockHandleClick = vi.fn();
const mockInternalHandler = vi.fn();
const mockOnInitial = vi.fn();
const mockOnErrorCallback = vi.fn();

beforeEach(() => {
  vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
    _handleOnClick: mockInternalHandler,
    suggestOfferResponse: mockOfferResponse,
    isLoading: false,
    error: undefined,
    debouncedInitial: vi.fn(),
  }));
});

afterEach(() => {
  vi.clearAllMocks();
});

describe("FinSuggestSOFButton", () => {
  it("renders offer content when suggestOfferResponse is available", () => {
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    // Should show the payment term text from the offer
    expect(
      screen.getByText(mockOfferResponse.offers?.[0]?.display_info.payment_term_text || "")
    ).toBeInTheDocument();
    
    // Should show the charge amount text
    expect(
      screen.getByText(mockOfferResponse.offers?.[0]?.display_info.charge_amount_text || "")
    ).toBeInTheDocument();
  });

  it("renders children when provided", () => {
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
        <span>Custom Content</span>
      </FinSuggestSOFButton>
    );
    
    expect(screen.getByText("Custom Content")).toBeInTheDocument();
    // Should not show default offer content when children are provided
    expect(
      screen.queryByText(mockOfferResponse.offers?.[0]?.display_info.payment_term_text || "")
    ).not.toBeInTheDocument();
  });

  it("calls onPress when clicked", () => {
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
        Test Button
      </FinSuggestSOFButton>
    );
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(mockInternalHandler).toHaveBeenCalledTimes(1);
  });

  it("spreads additional props to button element", () => {
    render(
      <FinSuggestSOFButton
        orders={mockOrders}
        onPress={mockHandleClick}
        data-testid="test-button"
        aria-label="test button"
      >
        Test Button
      </FinSuggestSOFButton>
    );
    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("data-testid", "test-button");
    expect(button).toHaveAttribute("aria-label", "test button");
  });

  it("shows loading skeleton when isLoading is true", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: true,
      error: undefined,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("data-loading", "true");
    expect(button.querySelector(".fin-suggest-sof-button__icon-skeleton")).toBeInTheDocument();
  });

  it("shows error content when error occurs", () => {
    const errorMessage = "Failed to fetch offer";
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: errorMessage,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    expect(screen.getByText("Không khả dụng")).toBeInTheDocument();
    expect(screen.getByText("Không có đề xuất trả sau cho đơn hàng này")).toBeInTheDocument();
  });

  it("renders error when no offer", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: undefined,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    // Button should be present but empty (no content)
    expect(button).toBeInTheDocument();
    expect(screen.getByText("Không có đề xuất trả sau cho đơn hàng này")).toBeInTheDocument();
  });

  it("is disabled when error occurs", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: "Some error",
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("is disabled when no offer is available", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: undefined,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("passes onInitial and onErrorCallback to hook", () => {
    render(
      <FinSuggestSOFButton
        orders={mockOrders}
        onPress={mockHandleClick}
        onInitial={mockOnInitial}
        onErrorCallback={mockOnErrorCallback}
      />
    );

    expect(hooks.useSuggestSOFButton).toHaveBeenCalledWith(
      expect.objectContaining({
        onPress: mockHandleClick,
        onInitial: mockOnInitial,
        onErrorCallback: mockOnErrorCallback,
        orders: mockOrders,
      })
    );
  });

  describe("Fallback scenarios", () => {
    const fallbackContent = <div data-testid="fallback-content">Custom Fallback</div>;

    it("renders fallback when error occurs and fallback is provided", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: false,
        error: "API Error",
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        />
      );

      expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
      expect(screen.getByText("Custom Fallback")).toBeInTheDocument();
      // Should not render the button when fallback is shown
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("renders fallback when no offer is available and fallback is provided", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: false,
        error: undefined,
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        />
      );

      expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
      expect(screen.getByText("Custom Fallback")).toBeInTheDocument();
      // Should not render the button when fallback is shown
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("renders fallback when offer status is not available and fallback is provided", () => {
      const firstOffer = mockOfferResponse.offers?.[0];
      const unavailableOfferResponse = {
        ...mockOfferResponse,
        offers: firstOffer ? [{
          ...firstOffer,
          status: "OFFER_STATUS_UNAVAILABLE" // Not OFFER_STATUS_AVAILABLE
        }] : []
      };

      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: unavailableOfferResponse,
        isLoading: false,
        error: undefined,
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        />
      );

      expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
      expect(screen.getByText("Custom Fallback")).toBeInTheDocument();
      // Should not render the button when fallback is shown
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("returns null when loading and fallback is provided", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: true,
        error: undefined,
        debouncedInitial: vi.fn(),
      }));

      const { container } = render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        />
      );

      // Should render nothing (null) when loading with fallback
      expect(container.firstChild).toBeNull();
      expect(screen.queryByTestId("fallback-content")).not.toBeInTheDocument();
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("renders button with error content when no fallback is provided", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: false,
        error: "API Error",
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          // No fallback prop provided
        />
      );

      // Should render the button with error content
      expect(screen.getByRole("button")).toBeInTheDocument();
      expect(screen.getByText("Không khả dụng")).toBeInTheDocument();
      expect(screen.getByText("Không có đề xuất trả sau cho đơn hàng này")).toBeInTheDocument();
    });
  });

  describe("Children scenarios", () => {
    it("renders children with successful offer when children are provided", () => {
      render(
        <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
          <span data-testid="custom-children">Custom Button Content</span>
        </FinSuggestSOFButton>
      );

      expect(screen.getByTestId("custom-children")).toBeInTheDocument();
      expect(screen.getByText("Custom Button Content")).toBeInTheDocument();
      // Should not show default offer content when children are provided
      expect(
        screen.queryByText(mockOfferResponse.offers?.[0]?.display_info.payment_term_text || "")
      ).not.toBeInTheDocument();
      // Button should still be present
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("renders children during loading when children are provided", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: true,
        error: undefined,
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
          <span data-testid="custom-children">Loading Custom Content</span>
        </FinSuggestSOFButton>
      );

      expect(screen.getByTestId("custom-children")).toBeInTheDocument();
      expect(screen.getByText("Loading Custom Content")).toBeInTheDocument();
      // Should not show loading skeleton when children are provided
      expect(screen.queryByText("fin-suggest-sof-button__icon-skeleton")).not.toBeInTheDocument();
      // Button should still be present
      expect(screen.getByRole("button")).toBeInTheDocument();
      expect(screen.getByRole("button")).toHaveAttribute("data-loading", "true");
    });

    it("renders children with error when children are provided", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: false,
        error: "API Error",
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
          <span data-testid="custom-children">Error Custom Content</span>
        </FinSuggestSOFButton>
      );

      expect(screen.getByTestId("custom-children")).toBeInTheDocument();
      expect(screen.getByText("Error Custom Content")).toBeInTheDocument();
      // Should not show error content when children are provided
      expect(screen.queryByText("Không khả dụng")).not.toBeInTheDocument();
      // Button should still be present but disabled
      expect(screen.getByRole("button")).toBeInTheDocument();
      expect(screen.getByRole("button")).toBeDisabled();
    });
  });

  describe("Fallback vs Children priority", () => {
    const fallbackContent = <div data-testid="fallback-content">Fallback Content</div>;

    it("prioritizes fallback over children when error occurs", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: false,
        error: "API Error",
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        >
          <span data-testid="custom-children">Children Content</span>
        </FinSuggestSOFButton>
      );

      // Should show fallback, not children
      expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
      expect(screen.queryByTestId("custom-children")).not.toBeInTheDocument();
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("prioritizes fallback over children when no offer available", () => {
      vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
        _handleOnClick: mockInternalHandler,
        suggestOfferResponse: undefined,
        isLoading: false,
        error: undefined,
        debouncedInitial: vi.fn(),
      }));

      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        >
          <span data-testid="custom-children">Children Content</span>
        </FinSuggestSOFButton>
      );

      // Should show fallback, not children
      expect(screen.getByTestId("fallback-content")).toBeInTheDocument();
      expect(screen.queryByTestId("custom-children")).not.toBeInTheDocument();
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("renders children when offer is available even with fallback prop", () => {
      // Successful offer scenario
      render(
        <FinSuggestSOFButton
          orders={mockOrders}
          onPress={mockHandleClick}
          fallbackComp={fallbackContent}
        >
          <span data-testid="custom-children">Children Content</span>
        </FinSuggestSOFButton>
      );

      // Should show children since offer is available
      expect(screen.getByTestId("custom-children")).toBeInTheDocument();
      expect(screen.queryByTestId("fallback-content")).not.toBeInTheDocument();
      expect(screen.getByRole("button")).toBeInTheDocument();
    });
  });
});
