import { CurrentLocation } from ".";
import { useLocation } from "./useLocation";
import { cn } from "@/lib/utils";
import "./location-picker.scss"
import { useTracking } from "@/hooks/useTracking";
import { FinSDKLocationTrackingId, TargetId } from "@/constants/trackingId";
export interface LocationPickerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  onLocationChange: (result: CurrentLocation) => void;
  title?: string;
  screenMetadata?: Record<string, any>;
}
export const LocationPicker = ({
  onLocationChange,
  screenMetadata,
  ...props
}: LocationPickerProps) => {
  const { getCurrentLocation } = useLocation();
  const trackEvent = useTracking(TargetId.FinSDK).trackEvent;
  const handleGetCurrentLocation = async () => {
    const result = await getCurrentLocation();
    trackEvent(FinSDKLocationTrackingId.GrantAccessLocation, {
      location: {
        lat: result?.location?.latitude,
        long: result?.location?.longitude,
      },
      ...screenMetadata
    })
    if (result) {
      onLocationChange(result);
    }
  };

  return (
    <button
      onClick={handleGetCurrentLocation}
      {...props}
      className={cn("location-picker", props.className ,{
        "location-picker_has-title": !!props?.title
      })}
    >
      {props?.children ? (
        props.children
      ) : (
        <>
          {props?.title ? <span className="location-picker_title">{props.title}</span> : null}
          <LocationIcon />
        </>
      )}
    </button>
  );
};

const LocationIcon = () => {
  return (
    // biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 1.25C12.4142 1.25 12.75 1.58579 12.75 2V3.78363C16.7079 4.14029 19.8597 7.29215 20.2164 11.25H22C22.4142 11.25 22.75 11.5858 22.75 12C22.75 12.4142 22.4142 12.75 22 12.75H20.2164C19.8597 16.7079 16.7079 19.8597 12.75 20.2164V22C12.75 22.4142 12.4142 22.75 12 22.75C11.5858 22.75 11.25 22.4142 11.25 22V20.2164C7.29215 19.8597 4.14029 16.7079 3.78363 12.75H2C1.58579 12.75 1.25 12.4142 1.25 12C1.25 11.5858 1.58579 11.25 2 11.25H3.78363C4.14029 7.29215 7.29215 4.14029 11.25 3.78363V2C11.25 1.58579 11.5858 1.25 12 1.25ZM5.25 12C5.25 8.27208 8.27208 5.25 12 5.25C15.7279 5.25 18.75 8.27208 18.75 12C18.75 15.7279 15.7279 18.75 12 18.75C8.27208 18.75 5.25 15.7279 5.25 12ZM9.75 12C9.75 10.7574 10.7574 9.75 12 9.75C13.2426 9.75 14.25 10.7574 14.25 12C14.25 13.2426 13.2426 14.25 12 14.25C10.7574 14.25 9.75 13.2426 9.75 12ZM12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25Z"
        fill="#0033C9"
      />
    </svg>
  );
};
