export enum TargetId {
  FinSDK = "7100",
  FinSuggestSOFButton = "6750",
}


export enum FinSDKLocationTrackingId {
    GrantAccessLocation = "000"
}

export enum FinSuggestSOFButtonTrackingId {
  //partner = <cimb/lfvn/zalopay>, status = <succeed/fail>, 
  // succeed: get thành công trạng thái của button, 
  // fail: get thất bại hoặc timeout, 
  // state = <enable/disable>, 
  // error_code
  LoadButton = "000",
//   partner = <cimb/lfvn/zalopay>
//   state = <enable/disable>
//   enable: thoả điều kiện show button
//   disable: bao gồm cả bị disable và 
//   displayed_sof (nguồn tiền hiển thị trên button)
//   promotion = <voucher/cashback/loyalty>
//   app_id (app_id tương ứng đang show button)
//   flow = <payment/onboarding/renewal/unlock>
//   payment: flow payment bình thường
//   onboarding: flow embedded onboarding
//   renewal: flow embedded renewal
//   unlock: flow embedded unlock
  VisibleButton = "001",
//   partner = <cimb/lfvn/zalopay>
//   state = <enable/disable>
//   displayed_sof (nguồn tiền hiển thị trên button)
//   promotion = <voucher/cashback/loyalty>
//   app_id (app_id tương ứng đang show button)
//   flow = <payment/onboarding/renewal/unlock>
  ClickButton = "002"
}