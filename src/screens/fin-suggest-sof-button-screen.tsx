import IphoneMockup from "@/components/iphone-mockup";
import { OrderItem } from "@/features/fin-suggest-sof-button";
import FinSuggestSOFButton from "@/features/fin-suggest-sof-button/r17-fin-suggest-sof-button";
import PlusMenu from "@/features/plus-menu";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { ShoppingCart, Package, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import pkg from "./../../package.json";

const FinSuggestSOFButtonScreen = () => {
  const isMobile = useIsMobile();
  const Wrapper = isMobile ? "div" : IphoneMockup;
  const [orders, setOrders] = useState<OrderItem[]>([]);
  const [formData, setFormData] = useState<OrderItem>({
  app_id: 0,
    amount: 0,
  });
  const [errors, setErrors] = useState<{
    app_id?: string;
    amount?: string;
  }>({});

  const handleOrderTelco = () => {
    const orders = [
      {
        app_id: 5000000,
        amount: 100,
      },
    ];
    setOrders(orders);
  };

  const handleOrder2 = () => {
    const orders = [
      {
        app_id: 6000000,
        amount: 1200,
      },
    ];
    setOrders(orders);
  };

  const handleOrder3 = () => {
    const orders = [
      {
        app_id: 10000000,
        amount: 12002,
      },
    ];
    setOrders(orders);
  };

  const handleOrder4 = () => {
    const orders = [
      {
        app_id: 11000000,
        amount: 12002,
      },
    ];
    setOrders(orders);
  };

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};
    
    if (!formData.app_id || formData.app_id <= 0) {
      newErrors.app_id = "App ID must be greater than 0";
    }
    
    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = "Amount must be greater than 0";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof OrderItem, value: string) => {
    const numValue = Number.parseInt(value) || 0;
    setFormData(prev => ({ ...prev, [field]: numValue }));
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      setOrders(prev => [...prev, formData]);
      // Reset form
      setFormData({ app_id: 0, amount: 0 });
      setErrors({});
    }
  };

  const handleClearOrders = () => {
    setOrders([]);
  };

  const handleRemoveOrder = (index: number) => {
    setOrders(prev => prev.filter((_, i) => i !== index));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', { 
      style: 'currency', 
      currency: 'VND' 
    }).format(amount);
  };
  return (
    <>
      <div className="w-full min-h-screen bg-gray-50">
        <div className={cn({ "max-h-[812px]": !isMobile })}>
          <Wrapper
            fullScreen
            className="aspect-[8/16] w-full max-w-[430px] overflow-hidden mx-auto relative h-screen bg-white"
          >
            <div className="relative flex flex-col h-full overflow-hidden">
              {/* Header */}
              <div className={cn("p-4 text-white shadow-md bg-gradient-to-r from-blue-600 to-blue-700", { "pt-14": !isMobile })}>
                <h1 className="flex items-center gap-2 text-xl font-bold">
                  <ShoppingCart className="w-5 h-5" />
                  Order Management
                </h1>
              </div>

              {/* Scrollable Content */}
              <div className="flex-1 pb-32 overflow-y-auto">
                <div className="p-4 space-y-4">
                  {/* Quick Actions */}
                  <div className="p-4 bg-white border border-gray-100 shadow-sm rounded-xl">
                    <h2 className="mb-3 text-sm font-semibold tracking-wide text-gray-600 uppercase">Quick Actions</h2>
                    <div className="grid grid-cols-2 gap-2">
                      <Button 
                        onClick={handleOrderTelco} 
                        variant="outline" 
                        size="sm"
                        className="justify-start hover:bg-blue-50 hover:border-blue-300"
                      >
                        <Package className="w-3 h-3 mr-1" />
                        5.000.000đ
                      </Button>
                      <Button 
                        onClick={handleOrder2} 
                        variant="outline" 
                        size="sm"
                        className="justify-start hover:bg-blue-50 hover:border-blue-300"
                      >
                        <Package className="w-3 h-3 mr-1" />
                        6.000.000đ
                      </Button>
                      <Button 
                        onClick={handleOrder3} 
                        variant="outline" 
                        size="sm"
                        className="justify-start hover:bg-blue-50 hover:border-blue-300"
                      >
                        <Package className="w-3 h-3 mr-1" />
                        10.000.000đ
                      </Button>
                      <Button 
                        onClick={handleOrder4} 
                        variant="outline" 
                        size="sm"
                        className="justify-start hover:bg-blue-50 hover:border-blue-300"
                      >
                        <Package className="w-3 h-3 mr-1" />
                        11.000.000đ
                      </Button>
                    </div>
                  </div>

                  {/* Order Form */}
                  <div className="p-4 bg-white border border-gray-100 shadow-sm rounded-xl">
                    <div className="flex items-center justify-between">
                      <h3 className="mb-4 text-sm font-semibold tracking-wide text-gray-600">Add New Order</h3>
                      <Button variant="outline" className="active:scale-[98%] bg-gray-100" onClick={() => {
                        setFormData({ app_id: Math.round(Math.random() + 1), amount: Math.round(Math.random() * 1000000)});
                      }}>
                        {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-dices-icon lucide-dices"><rect width="12" height="12" x="2" y="10" rx="2" ry="2"/><path d="m17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6"/><path d="M6 18h.01"/><path d="M10 14h.01"/><path d="M15 6h.01"/><path d="M18 9h.01"/></svg>
                        
                      </Button>
                    </div>
                    <form onSubmit={handleSubmit} className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label htmlFor="app_id" className="block mb-1 text-xs font-medium text-gray-700">
                            App ID
                          </label>
                          <input
                            type="number"
                            id="app_id"
                            value={formData.app_id || ''}
                            onChange={(e) => handleInputChange('app_id', e.target.value)}
                            className={cn(
                              "w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors",
                              errors.app_id ? "border-red-500 bg-red-50" : "border-gray-200"
                            )}
                            placeholder="e.g. 5000000"
                          />
                          {errors.app_id && (
                            <p className="mt-1 text-xs text-red-600">{errors.app_id}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="amount" className="block mb-1 text-xs font-medium text-gray-700">
                            Amount (VND)
                          </label>
                          <input
                            type="number"
                            id="amount"
                            value={formData.amount || ''}
                            onChange={(e) => handleInputChange('amount', e.target.value)}
                            className={cn(
                              "w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors",
                              errors.amount ? "border-red-500 bg-red-50" : "border-gray-200"
                            )}
                            placeholder="e.g. 100000"
                          />
                          {errors.amount && (
                            <p className="mt-1 text-xs text-red-600">{errors.amount}</p>
                          )}
                        </div>
                      </div>

                      <div className="flex w-full gap-2">
                        <Button 
                          type="submit" 
                          className="flex-1 font-medium text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Order
                        </Button>
                       
                      </div>
                    </form>
                  </div>

                  {/* Orders List */}
                  {orders.length > 0 && (
                    <div className="p-4 bg-white border border-gray-100 shadow-sm rounded-xl">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-sm font-semibold tracking-wide text-gray-600 uppercase">
                          Order List ({orders.length})
                        </h3>
                        <div className="flex items-center justify-end space-x-2">
                        <Button 
                          size="sm"
                          variant="ghost" 
                          onClick={handleClearOrders}
                          className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          Clear All
                        </Button>
                         <Button 
                          type="button"
                          
                          onClick={() => {
                            if (orders.length > 0) {
                              const tempOrders = structuredClone(orders);
                              setOrders([]);
                              setOrders(tempOrders);
                            }
                          }}
                          disabled={orders.length === 0}
                          variant="default"
                          className="px-3 active:scale-[98%]"
                          title="Recall last order"
                        >
                          Recall                        </Button>
                          </div>
                      </div>
                      
                      <div className="space-y-2">
                        {orders.map((order, index) => (
                          <div 
                            key={index} 
                            className="flex items-center justify-between p-3 transition-colors rounded-lg bg-gray-50 hover:bg-gray-100 group"
                          >
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                                <span className="text-xs font-semibold text-blue-600">
                                  {index + 1}
                                </span>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">App ID: {order.app_id}</p>
                                <p className="text-sm font-semibold">{formatCurrency(order.amount)}</p>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRemoveOrder(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>

                      {/* Total Summary */}
                      <div className="pt-4 mt-4 border-t border-gray-200">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">Total Amount</span>
                          <span className="text-lg font-bold text-blue-600">
                            {formatCurrency(orders.reduce((sum, order) => sum + order.amount, 0))}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Empty State */}
                  {orders.length === 0 && (
                    <div className="p-8 text-center bg-white border border-gray-100 shadow-sm rounded-xl">
                      <ShoppingCart className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                      <p className="text-sm text-gray-500">No orders yet</p>
                      <p className="mt-1 text-xs text-gray-400">Add your first order using the form above</p>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Fixed Bottom Payment Section */}
              <div className="absolute bottom-0 left-0 right-0 p-4 pb-0 bg-white border-t border-gray-200 shadow-lg">
                <div className="flex justify-end gap-3">
                  <FinSuggestSOFButton
                    options={{
                      latency: 500,
                    }}
                    fallbackComp={<div className="flex justify-center flex-1 text-base font-bold text-white bg-gray-300 rounded-lg cursor-not-allowed">No payment options</div>}
                    className="flex-1 active:scale-[98%] transition-transform ease-in-out duration-300"
                    disabled={orders.length === 0}
                    onPress={console.log}
                    orders={orders}
                  />
                  <Button 
                    className="h-12 rounded-lg px-6 font-bold text-white bg-primary hover:bg-blue-700 active:scale-[98%] disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={orders.length === 0}
                  >
                    Thanh toán
                  </Button>
                </div>
                <div className="w-full">
                <p className="p-0.5 text-center text-xs text-gray-500">version {pkg.version}</p>
                </div>
              </div>
            </div>
          </Wrapper>
        </div>
      </div>
      <PlusMenu />
    </>
  );
};

export default FinSuggestSOFButtonScreen;