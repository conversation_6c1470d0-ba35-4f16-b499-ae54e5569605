import { createBrowserRouter, RouteObject } from "react-router-dom";
import loadable from "@loadable/component";
import { Layout } from "./_layout";

const FinSuggestSOFButtonScreenLazy = loadable(() => import("@/screens/fin-suggest-sof-button-screen"));
const HomeScreenLazy = loadable(() => import("@/screens/home-screen"));
const AILazy = loadable(() => import("@/screens/ai"));
const LocationScreenLazy = loadable(() => import("@/screens/location-screen"));

const ErrorPage = () => {
  return (
    <div>
      <h2>404 - Page Not Found</h2>
      <p>The page you&apos;re looking for doesn&apos;t exist.</p>
    </div>
  );
};

const mainRoutes: RouteObject[] = [
  {
    path: "/fin-suggest-sof-button",
    element: <FinSuggestSOFButtonScreenLazy />
  },
  {
    path: "/home-screen",
    element: <HomeScreenLazy />
  },
  {
    path: "/",
    element: <HomeScreenLazy />
  },
   {
    path: "/ai",
    element: <AILazy />
  },
  {
    path: "/location-screen",
    element: <LocationScreenLazy />
  },
]
const routes: RouteObject[] = [
  {
    path: "/",
    element: <Layout />,
    errorElement: <ErrorPage />,
    children: mainRoutes
  }
];
const appPath = '/fin-sdk';
const isRunLocal = !location.pathname.includes(appPath)
export const appOrigin = window.location.origin;
export const appBase = isRunLocal ? "/test-app" : appPath;
export const baseName = window.__BASE_NAME__ || "/spa/v2";
export const rootBase = `${baseName}${appBase}`;

const router = createBrowserRouter(routes, {
  basename: rootBase,
});

export default router;
