{"name": "fin-sdk", "version": "1.0.2", "private": true, "type": "module", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --open", "preview": "rsbuild preview", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@loadable/component": "^5.16.7", "@module-federation/bridge-react": "^0.14.3", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@silk-hq/components": "^0.9.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.510.0", "motion": "^12.16.0", "next-themes": "^0.4.6", "query-string": "^9.2.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "single-spa-react": "^6.0.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.2.9", "vaul": "^1.1.2", "zod": "^3.24.4", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@rsbuild/core": "^1.3.15", "@rsbuild/plugin-react": "^1.3.0", "@rsbuild/plugin-sass": "^1.3.1", "@tailwindcss/postcss": "^4.1.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/loadable__component": "^5.13.9", "@types/node": "^22.15.19", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@vitest/coverage-istanbul": "^3.2.4", "autoprefixer": "^10.4.21", "happy-dom": "^17.4.7", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "shiki": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "packageManager": "pnpm@8.15.5"}