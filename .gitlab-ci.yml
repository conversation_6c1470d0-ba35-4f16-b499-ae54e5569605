stages:
  - install_dependencies
  - test
  - deploy

variables:
  VERDACCIO_ZTOOL_TOKEN: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWFsX2dyb3VwcyI6WyJ0cnVjbmMiXSwibmFtZSI6InRydWNuYyIsImdyb3VwcyI6WyJ0cnVjbmMiLCIkYWxsIiwiJGF1dGhlbnRpY2F0ZWQiLCJAYWxsIiwiQGF1dGhlbnRpY2F0ZWQiLCJhbGwiLCJ0cnVjbmMiXSwiaWF0IjoxNzE3NDg3NjkwLCJuYmYiOjE3MTc0ODc2OTEsImV4cCI6MTc0OTAyMzY5MH0.E3hL-g1co6UsqFk_dQGzcNfBH6F88j9cCmRSnZU0GGU"
# Default image for all jobs
default:
  image: nikolaik/python-nodejs:python3.13-nodejs23-alpine	
  cache: &cache
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
    policy: pull-push

before_script:
    - npm install --global corepack@latest
    - corepack enable
    - corepack prepare pnpm@latest-10 --activate
    - pnpm config set store-dir .pnpm-store


install_dependencies:
  stage: install_dependencies
  tags:
    - docker-executor-7118
  cache:
    <<: *cache
    policy: push
  script:
    - pnpm install
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - pnpm-lock.yaml
        - package.json
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - pnpm-lock.yaml
        - package.json
    - if: '$CI_COMMIT_REF_NAME =~ /^releases\/.+$/'
      changes:
        - pnpm-lock.yaml
        - package.json


test:
  stage: test
  tags:
    - docker-executor-7118
  script:
    - |
      # Install dependencies if cache exists but install_dependencies didn't run
      if [ ! -d "node_modules" ]; then
        echo "Dependencies not found, installing..."
        pnpm install
      fi
    - pnpm run test
  needs:
    - job: install_dependencies
      optional: true
  coverage: '/Coverage: (\d+\.\d+%)/'
  only:
    - main
    - merge_requests
    - /^releases\/.+$/

deploy_qc:
  stage: deploy
  when: manual
  tags:
    - docker-executor-7118
  script:
    - |
      # Install dependencies if cache exists but install_dependencies didn't run
      if [ ! -d "node_modules" ]; then
        echo "Dependencies not found, installing..."
        pnpm install
      fi
    - sh scripts/build-deploy-zpi.sh qc
  needs:
    - job: install_dependencies
      optional: true
  artifacts:
    paths:
      - dist/
  only:
    - /^(releases\/|features\/|bugs\/).+$/
  cache:
    <<: *cache
    policy: pull
       

# Example staging deployment
deploy-staging:
  stage: deploy
  when: manual
  tags:
    - docker-executor-7118
  script:
    - |
      # Install dependencies if cache exists but install_dependencies didn't run
      if [ ! -d "node_modules" ]; then
        echo "Dependencies not found, installing..."
        pnpm install
      fi
    - sh scripts/build-deploy-zpi.sh stg
  needs:
    - job: install_dependencies
      optional: true
  artifacts:
    paths:
      - dist/
  only:
    - /^(releases\/|features\/|bugs\/).+$/
  cache:
      <<: *cache
      policy: pull
      
deploy_jenkins_prod:
  cache: []
  when: manual
  stage: deploy
  script:
    - sh scripts/deployment.sh pro $PRIVATE_TOKEN
  only:
    - /^releases\/.+$/
  needs:
    - job: install_dependencies
      optional: true
    - test
  tags:
    - shell-executor-7118
  before_script: []
