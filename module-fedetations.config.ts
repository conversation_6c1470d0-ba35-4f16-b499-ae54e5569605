import path from "node:path";

const FinSuggestSOFButton = {
    "./FinSuggestSOFButton": path.join(__dirname, "./src/features/fin-suggest-sof-button/index.tsx"),
    "./R17FinSuggestSOFButton": path.join(__dirname, "./src/features/fin-suggest-sof-button/r17-fin-suggest-sof-button.tsx"),
}

const LocationPicker = {
    "./LocationPicker": path.join(__dirname, "./src/features/location-picker/index.tsx"),
    "./R17LocationPicker": path.join(__dirname, "./src/features/location-picker/r17-location-picker.tsx"),
}

export default {
    options: {
      // biome-ignore lint/style/noNonNullAssertion: <explanation>
      name: process.env.APP_NAME!,
      filename: "remoteEntry.js",
      exposes: {
        // biome-ignore lint/style/noNonNullAssertion: <explanation>
        [process.env.APP_PATH!]: path.join(__dirname, "./src/zpi-app.tsx"),
        ...FinSuggestSOFButton,
        ...LocationPicker,
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: "18.3.1",
          shareKey: "react@18",
          eager: true,
        },
        "react-dom": {
          singleton: true,
          requiredVersion: "18.3.1",
          shareKey: "react-dom@18",
          eager: true,
        },
        // "react-router-dom": {
        //   singleton: true,
        //   requiredVersion: deps["react-router-dom"],
        // },
      },
    },
  };
  